import {
  initializeConsentMode,
  savePreferences,
  getSavedPreferences,
  hasUserMadeChoice,
  acceptAllCookies,
  rejectAllCookies,
  preferencesToConsent,
  updateConsentMode,
  DEFAULT_CONSENT,
  DEFAULT_PREFERENCES
} from '../consent'

// Mock do localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('consent management', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    global.gtag = jest.fn() as jest.MockedFunction<typeof global.gtag>
    global.dataLayer = []
  })

  describe('initializeConsentMode', () => {
    it('should initialize consent mode with default values', () => {
      initializeConsentMode()
      
      expect(global.gtag).toHaveBeenCalledWith('consent', 'default', {
        ...DEFAULT_CONSENT,
        wait_for_update: 500,
      })
    })

    it('should apply saved preferences if they exist', () => {
      const savedPrefs = {
        essential: true,
        analytics: true,
        marketing: false,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedPrefs))
      
      initializeConsentMode()
      
      expect(global.gtag).toHaveBeenCalledWith('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
      })
    })
  })

  describe('savePreferences', () => {
    it('should save preferences to localStorage', () => {
      const preferences = {
        essential: true,
        analytics: true,
        marketing: false,
        timestamp: 123456789,
        version: '1.0'
      }

      savePreferences(preferences)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'cereja_consent_preferences',
        expect.stringContaining('"analytics":true')
      )
    })

    it('should handle localStorage errors gracefully', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      savePreferences(DEFAULT_PREFERENCES)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Erro ao salvar preferências de cookies:',
        expect.any(Error)
      )
      
      consoleSpy.mockRestore()
    })
  })

  describe('getSavedPreferences', () => {
    it('should return null if no preferences saved', () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      expect(getSavedPreferences()).toBeNull()
    })

    it('should return saved preferences', () => {
      const preferences = {
        essential: true,
        analytics: false,
        marketing: true,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(preferences))
      
      expect(getSavedPreferences()).toEqual(preferences)
    })

    it('should return null for incompatible version', () => {
      const preferences = {
        essential: true,
        analytics: false,
        marketing: true,
        timestamp: Date.now(),
        version: '0.9' // versão antiga
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(preferences))
      
      expect(getSavedPreferences()).toBeNull()
    })

    it('should handle JSON parse errors', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json')
      
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      expect(getSavedPreferences()).toBeNull()
      expect(consoleSpy).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })
  })

  describe('hasUserMadeChoice', () => {
    it('should return false if no preferences saved', () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      expect(hasUserMadeChoice()).toBe(false)
    })

    it('should return true if preferences exist', () => {
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(DEFAULT_PREFERENCES))
      
      expect(hasUserMadeChoice()).toBe(true)
    })
  })

  describe('acceptAllCookies', () => {
    it('should accept all cookies and save preferences', () => {
      acceptAllCookies()
      
      expect(global.gtag).toHaveBeenCalledWith('consent', 'update', {
        analytics_storage: 'granted',
        ad_storage: 'granted',
        ad_user_data: 'granted',
        ad_personalization: 'granted',
      })
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })
  })

  describe('rejectAllCookies', () => {
    it('should reject all non-essential cookies', () => {
      rejectAllCookies()
      
      expect(global.gtag).toHaveBeenCalledWith('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
      })
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })
  })

  describe('preferencesToConsent', () => {
    it('should convert preferences to consent config', () => {
      const preferences = {
        essential: true,
        analytics: true,
        marketing: false,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      const result = preferencesToConsent(preferences)
      
      expect(result).toEqual({
        analytics_storage: 'granted',
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
      })
    })
  })

  describe('updateConsentMode', () => {
    it('should update consent mode and save preferences', () => {
      const preferences = {
        essential: true,
        analytics: false,
        marketing: true,
        timestamp: Date.now(),
        version: '1.0'
      }
      
      updateConsentMode(preferences)
      
      expect(global.gtag).toHaveBeenCalledWith('consent', 'update', {
        analytics_storage: 'denied',
        ad_storage: 'granted',
        ad_user_data: 'granted',
        ad_personalization: 'granted',
      })
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('should handle missing gtag gracefully', () => {
      global.gtag = undefined
      
      const preferences = DEFAULT_PREFERENCES
      
      expect(() => updateConsentMode(preferences)).not.toThrow()
    })
  })
})
