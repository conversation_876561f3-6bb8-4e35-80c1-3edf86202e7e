{"name": "cereja", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest lib/__tests__ components/__tests__/ConsentManager.test.tsx components/__tests__/GoogleAdSense.test.tsx components/__tests__/GoogleAnalytics.test.tsx", "test:working": "jest lib/__tests__/utils.test.ts lib/__tests__/analytics.test.ts components/__tests__/ConsentManager.test.tsx components/__tests__/GoogleAdSense.test.tsx components/__tests__/GoogleAnalytics.test.tsx"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "typescript": "^5"}}