import { render, waitFor } from '@testing-library/react'
import { GoogleAdSense } from '../GoogleAdSense'

describe('GoogleAdSense', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Limpar scripts existentes
    document.head.innerHTML = ''
    // Reset window.adsbygoogle
    delete (window as any).adsbygoogle
  })

  it('should not load AdSense script if already exists', () => {
    // Adicionar script existente
    const existingScript = document.createElement('script')
    existingScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js'
    document.head.appendChild(existingScript)
    
    render(<GoogleAdSense />)
    
    // Deve haver apenas um script
    const scripts = document.querySelectorAll('script[src*="adsbygoogle.js"]')
    expect(scripts).toHaveLength(1)
  })

  it('should render without errors', () => {
    expect(() => render(<GoogleAdSense />)).not.toThrow()
  })

  it('should handle ad blocker detection', async () => {
    // Mock para simular bloqueador de anúncios
    const mockFetch = jest.fn().mockRejectedValue(new Error('Blocked'))
    global.fetch = mockFetch
    
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
    
    render(<GoogleAdSense />)
    
    await waitFor(() => {
      // Em desenvolvimento, deve logar a detecção do bloqueador
      if (process.env.NODE_ENV === 'development') {
        expect(consoleSpy).toHaveBeenCalledWith(
          expect.stringContaining('Bloqueador de anúncios detectado')
        )
      }
    })
    
    consoleSpy.mockRestore()
  })

  it('should handle component lifecycle', () => {
    const { unmount } = render(<GoogleAdSense />)
    expect(() => unmount()).not.toThrow()
  })

  it('should handle multiple renders', () => {
    const { rerender } = render(<GoogleAdSense />)
    expect(() => {
      rerender(<GoogleAdSense />)
      rerender(<GoogleAdSense />)
    }).not.toThrow()
  })

  it('should handle environment changes', () => {
    const originalEnv = process.env.NODE_ENV

    // Testar em desenvolvimento
    process.env.NODE_ENV = 'development'
    expect(() => render(<GoogleAdSense />)).not.toThrow()

    // Testar em produção
    process.env.NODE_ENV = 'production'
    expect(() => render(<GoogleAdSense />)).not.toThrow()

    // Restaurar ambiente original
    process.env.NODE_ENV = originalEnv
  })
})
