import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ConsentManager } from '@/components/ConsentManager'

// Mock das funções de consent
jest.mock('@/lib/consent', () => ({
  hasUserMadeChoice: jest.fn(),
  acceptAllCookies: jest.fn(),
  rejectAllCookies: jest.fn(),
  updateConsentMode: jest.fn(),
  getSavedPreferences: jest.fn(),
}))

import {
  hasUserMadeChoice,
  acceptAllCookies,
  rejectAllCookies,
  updateConsentMode
} from '@/lib/consent'

describe('Consent Management Integration Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
    global.gtag = jest.fn()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should show banner for new users and handle acceptance', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    // Avançar timer para mostrar banner
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/cookies/i)).toBeInTheDocument()
    })
    
    // Aceitar todos os cookies
    const acceptButton = screen.getByText(/aceitar todos/i)
    await user.click(acceptButton)
    
    expect(acceptAllCookies).toHaveBeenCalled()
    
    // Banner deve desaparecer
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should handle cookie rejection flow', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/rejeitar todos/i)).toBeInTheDocument()
    })
    
    // Rejeitar todos os cookies
    const rejectButton = screen.getByText(/rejeitar todos/i)
    await user.click(rejectButton)
    
    expect(rejectAllCookies).toHaveBeenCalled()
    
    // Banner deve desaparecer
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should handle custom preferences flow', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/personalizar/i)).toBeInTheDocument()
    })
    
    // Abrir configurações personalizadas
    const customizeButton = screen.getByText(/personalizar/i)
    await user.click(customizeButton)
    
    // Modal de configurações deve aparecer
    await waitFor(() => {
      expect(screen.getByText(/configurações de cookies/i)).toBeInTheDocument()
    })
    
    // Verificar toggles de preferências
    expect(screen.getByText(/cookies essenciais/i)).toBeInTheDocument()
    expect(screen.getByText(/cookies de análise/i)).toBeInTheDocument()
    expect(screen.getByText(/cookies de marketing/i)).toBeInTheDocument()
    
    // Configurar preferências (aceitar apenas analytics)
    const analyticsToggle = screen.getByLabelText(/cookies de análise/i)
    await user.click(analyticsToggle)
    
    // Salvar configurações
    const saveButton = screen.getByText(/salvar configurações/i)
    await user.click(saveButton)
    
    expect(updateConsentMode).toHaveBeenCalledWith({
      essential: true,
      analytics: true,
      marketing: false,
      timestamp: expect.any(Number),
      version: '1.0'
    })
    
    // Modal deve fechar
    await waitFor(() => {
      expect(screen.queryByText(/configurações de cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should not show banner for returning users', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(true)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    // Banner não deve aparecer
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should handle modal close without saving', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/personalizar/i)).toBeInTheDocument()
    })
    
    // Abrir modal
    const customizeButton = screen.getByText(/personalizar/i)
    await user.click(customizeButton)
    
    await waitFor(() => {
      expect(screen.getByText(/configurações de cookies/i)).toBeInTheDocument()
    })
    
    // Fechar modal sem salvar
    const closeButton = screen.getByLabelText(/fechar/i) || screen.getByText(/×/i)
    await user.click(closeButton)
    
    // updateConsentMode não deve ser chamado
    expect(updateConsentMode).not.toHaveBeenCalled()
    
    // Modal deve fechar
    await waitFor(() => {
      expect(screen.queryByText(/configurações de cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should handle keyboard navigation in modal', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/personalizar/i)).toBeInTheDocument()
    })
    
    // Abrir modal
    const customizeButton = screen.getByText(/personalizar/i)
    await user.click(customizeButton)
    
    await waitFor(() => {
      expect(screen.getByText(/configurações de cookies/i)).toBeInTheDocument()
    })
    
    // Testar navegação por Tab
    await user.tab()
    
    // Primeiro elemento focável deve estar focado
    const focusedElement = document.activeElement
    expect(focusedElement).toBeInTheDocument()
    
    // Testar Escape para fechar
    await user.keyboard('{Escape}')
    
    await waitFor(() => {
      expect(screen.queryByText(/configurações de cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should persist preferences correctly', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    const mockLocalStorage = {
      setItem: jest.fn(),
      getItem: jest.fn(),
    }
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/aceitar todos/i)).toBeInTheDocument()
    })
    
    // Aceitar todos
    const acceptButton = screen.getByText(/aceitar todos/i)
    await user.click(acceptButton)
    
    // Verificar se localStorage foi chamado
    expect(mockLocalStorage.setItem).toHaveBeenCalled()
  })

  it('should handle consent mode updates correctly', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime })
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<ConsentManager />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/aceitar todos/i)).toBeInTheDocument()
    })
    
    // Aceitar todos
    const acceptButton = screen.getByText(/aceitar todos/i)
    await user.click(acceptButton)
    
    // Verificar se gtag foi chamado para atualizar consent
    expect(global.gtag).toHaveBeenCalledWith('consent', 'update', expect.any(Object))
  })
})
