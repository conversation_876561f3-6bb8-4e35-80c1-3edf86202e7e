import {
  initGA,
  trackPageView,
  trackEvent,
  trackButtonClick,
  trackRecipeGenerated,
  trackRecipeCompleted,
  trackRecipeShared,
  trackRecipeTimer
} from '../analytics'

// Mock do react-ga4
jest.mock('react-ga4', () => ({
  initialize: jest.fn(),
  send: jest.fn(),
  event: jest.fn(),
}))

import ReactGA from 'react-ga4'

// Mock das funções de consent
jest.mock('../consent', () => ({
  isCategoryAccepted: jest.fn(),
}))

import { isCategoryAccepted } from '../consent'

describe('analytics', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    global.gtag = jest.fn()
    ;(isCategoryAccepted as jest.Mock).mockReturnValue(true)
  })

  describe('initGA', () => {
    it('should initialize Google Analytics', () => {
      initGA()

      expect(ReactGA.initialize).toHaveBeenCalledWith('G-QCCMM3EWFJ', {
        testMode: false,
      })
    })

    it('should configure gtag', () => {
      initGA()

      expect(global.gtag).toHaveBeenCalledWith('config', 'G-QCCMM3EWFJ', {
        page_title: document.title,
        page_location: window.location.href,
      })
    })
  })

  describe('trackPageView', () => {
    it('should track page view when analytics accepted', () => {
      trackPageView('/test-page')

      expect(ReactGA.send).toHaveBeenCalledWith({
        hitType: 'pageview',
        page: '/test-page',
        title: document.title,
      })
    })

    it('should not track when analytics not accepted', () => {
      ;(isCategoryAccepted as jest.Mock).mockReturnValue(false)
      
      trackPageView('/test-page')
      
      expect(ReactGA.send).not.toHaveBeenCalled()
    })

    it('should handle missing window gracefully', () => {
      const originalWindow = global.window
      delete (global as any).window
      
      expect(() => trackPageView('/test')).not.toThrow()
      
      global.window = originalWindow
    })
  })

  describe('trackEvent', () => {
    it('should track custom events', () => {
      const parameters = { category: 'test', value: 1 }
      
      trackEvent('test_action', parameters)
      
      expect(global.gtag).toHaveBeenCalledWith('event', 'test_action', parameters)
      expect(ReactGA.event).toHaveBeenCalledWith('test_action', parameters)
    })

    it('should not track when analytics not accepted', () => {
      ;(isCategoryAccepted as jest.Mock).mockReturnValue(false)
      
      trackEvent('test_action')
      
      expect(global.gtag).not.toHaveBeenCalled()
      expect(ReactGA.event).not.toHaveBeenCalled()
    })
  })

  describe('trackButtonClick', () => {
    it('should track button clicks with correct parameters', () => {
      trackButtonClick('test_button', 'header')
      
      expect(global.gtag).toHaveBeenCalledWith('event', 'button_click', {
        button_name: 'test_button',
        location: 'header',
      })
    })
  })

  describe('trackRecipeGenerated', () => {
    it('should track recipe generation with parameters', () => {
      const recipeData = {
        cafe: 15,
        agua: 225,
        proporcao: 15,
        perfilSabor: 'Equilibrado',
        perfilCorpo: 'Equilibrado'
      }

      trackRecipeGenerated(recipeData)

      expect(global.gtag).toHaveBeenCalledWith('event', 'recipe_generated', {
        cafe_amount: 15,
        water_amount: 225,
        ratio: 15,
        flavor_profile: 'Equilibrado',
        body_profile: 'Equilibrado',
        recipe_type: 'v60_4_6_method',
      })
    })
  })

  describe('trackRecipeCompleted', () => {
    it('should track recipe completion', () => {
      trackRecipeCompleted(330, 6)

      expect(global.gtag).toHaveBeenCalledWith('event', 'recipe_completed', {
        completion_time_seconds: 330,
        total_steps: 6,
        success: true,
      })
    })
  })

  describe('trackRecipeShared', () => {
    it('should track recipe sharing', () => {
      trackRecipeShared('copy')

      expect(global.gtag).toHaveBeenCalledWith('event', 'share_recipe', {
        share_method: 'copy',
      })
    })
  })

  describe('trackRecipeTimer', () => {
    it('should track timer actions', () => {
      trackRecipeTimer('start', 60)

      expect(global.gtag).toHaveBeenCalledWith('event', 'recipe_timer', {
        timer_action: 'start',
        current_time_seconds: 60,
      })
    })
  })
})
