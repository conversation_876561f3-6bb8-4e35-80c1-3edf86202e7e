import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Home from '../page'

// Mock das funções de analytics
jest.mock('@/lib/analytics', () => ({
  trackButtonClick: jest.fn(),
  trackRecipeGenerated: jest.fn(),
  trackRecipeCompleted: jest.fn(),
  trackRecipeShared: jest.fn(),
  trackRecipeTimer: jest.fn(),
}))

describe('Home Page (Calculadora)', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render the main title', () => {
    render(<Home />)
    
    expect(screen.getByText('cereja')).toBeInTheDocument()
    expect(screen.getByText('calculadora do método 4:6')).toBeInTheDocument()
  })

  it('should render input fields with default values', () => {
    render(<Home />)
    
    expect(screen.getByDisplayValue('15')).toBeInTheDocument() // café
    expect(screen.getByDisplayValue('225')).toBeInTheDocument() // água
    expect(screen.getByDisplayValue('15')).toBeInTheDocument() // proporção
  })

  it('should allow changing coffee amount', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    
    await user.clear(cafeInput)
    await user.type(cafeInput, '20')
    
    expect(cafeInput).toHaveValue('20')
  })

  it('should update water amount when coffee changes and water is locked', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    const aguaInput = screen.getByLabelText(/quantidade de água/i)
    
    // Verificar que água está bloqueada por padrão
    expect(aguaInput).toHaveClass('cursor-not-allowed')
    
    await user.clear(cafeInput)
    await user.type(cafeInput, '20')
    
    // Água deve ser recalculada automaticamente (20 * 15 = 300)
    await waitFor(() => {
      expect(aguaInput).toHaveValue('300')
    })
  })

  it('should allow unlocking water input', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const aguaInput = screen.getByLabelText(/quantidade de água/i)
    const unlockButton = screen.getAllByRole('button').find(btn => 
      btn.querySelector('svg') // botão com ícone de lock/unlock
    )
    
    expect(aguaInput).toHaveClass('cursor-not-allowed')
    
    if (unlockButton) {
      await user.click(unlockButton)
      expect(aguaInput).not.toHaveClass('cursor-not-allowed')
    }
  })

  it('should render flavor profile options', () => {
    render(<Home />)
    
    expect(screen.getByText('Ácido')).toBeInTheDocument()
    expect(screen.getByText('Equilibrado')).toBeInTheDocument()
    expect(screen.getByText('Doce')).toBeInTheDocument()
  })

  it('should render body profile options', () => {
    render(<Home />)
    
    expect(screen.getByText('Leve')).toBeInTheDocument()
    expect(screen.getByText('Médio')).toBeInTheDocument()
    expect(screen.getByText('Encorpado')).toBeInTheDocument()
  })

  it('should allow selecting different flavor profiles', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const acidoButton = screen.getByText('Ácido')
    
    await user.click(acidoButton)
    
    expect(acidoButton).toHaveClass('bg-primary')
  })

  it('should allow selecting different body profiles', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const leveButton = screen.getByText('Leve')
    
    await user.click(leveButton)
    
    expect(leveButton).toHaveClass('bg-primary')
  })

  it('should generate recipe when button is clicked', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const generateButton = screen.getByText(/gerar receita/i)
    
    await user.click(generateButton)
    
    // Verificar se a receita foi gerada (deve aparecer etapas)
    await waitFor(() => {
      expect(screen.getByText(/etapa 1/i)).toBeInTheDocument()
    })
  })

  it('should show timer controls after generating recipe', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText(/iniciar/i)).toBeInTheDocument()
      expect(screen.getByText(/reiniciar/i)).toBeInTheDocument()
    })
  })

  it('should start timer when play button is clicked', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    // Gerar receita primeiro
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    await waitFor(() => {
      const playButton = screen.getByText(/iniciar/i)
      expect(playButton).toBeInTheDocument()
    })
    
    const playButton = screen.getByText(/iniciar/i)
    await user.click(playButton)
    
    // Verificar se o botão mudou para pausar
    await waitFor(() => {
      expect(screen.getByText(/pausar/i)).toBeInTheDocument()
    })
  })

  it('should show share button after generating recipe', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText(/compartilhar/i)).toBeInTheDocument()
    })
  })

  it('should handle invalid input values gracefully', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    
    await user.clear(cafeInput)
    await user.type(cafeInput, 'abc')
    
    // Input deve rejeitar valores não numéricos ou reverter para valor anterior
    expect(cafeInput).not.toHaveValue('abc')
  })

  it('should have proper accessibility attributes', () => {
    render(<Home />)
    
    // Verificar labels dos inputs
    expect(screen.getByLabelText(/quantidade de café/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/quantidade de água/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/proporção/i)).toBeInTheDocument()
    
    // Verificar botões têm texto acessível
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toBeVisible()
    })
  })

  it('should render navigation menu', () => {
    render(<Home />)
    
    const menuButton = screen.getByLabelText(/abrir menu/i)
    expect(menuButton).toBeInTheDocument()
  })

  it('should open navigation menu when clicked', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const menuButton = screen.getByLabelText(/abrir menu/i)
    await user.click(menuButton)
    
    // Menu deve aparecer
    await waitFor(() => {
      expect(screen.getByText(/glossário/i)).toBeInTheDocument()
    })
  })
})
