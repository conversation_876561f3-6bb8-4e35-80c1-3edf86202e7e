import { render } from '@testing-library/react'
import { GoogleAnalytics } from '../GoogleAnalytics'

// Mock das funções de analytics e consent
jest.mock('@/lib/analytics', () => ({
  initGA: jest.fn(),
  trackPageView: jest.fn(),
}))

jest.mock('@/lib/consent', () => ({
  initializeConsentMode: jest.fn(),
}))

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

import { initGA, trackPageView } from '@/lib/analytics'
import { initializeConsentMode } from '@/lib/consent'
import { usePathname } from 'next/navigation'

describe('GoogleAnalytics', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(usePathname as jest.Mock).mockReturnValue('/')
  })

  it('should initialize consent mode and GA on mount', () => {
    render(<GoogleAnalytics />)
    
    expect(initializeConsentMode).toHaveBeenCalled()
    expect(initGA).toHaveBeenCalled()
  })

  it('should track page view when pathname changes', () => {
    const { rerender } = render(<GoogleAnalytics />)
    
    // Simular mudança de pathname
    ;(usePathname as jest.Mock).mockReturnValue('/glossario')
    rerender(<GoogleAnalytics />)
    
    expect(trackPageView).toHaveBeenCalledWith('/glossario')
  })

  it('should track initial page view', () => {
    render(<GoogleAnalytics />)
    
    expect(trackPageView).toHaveBeenCalledWith('/')
  })

  it('should render Google Tag Manager script', () => {
    render(<GoogleAnalytics />)
    
    const scripts = document.querySelectorAll('script[src*="googletagmanager.com"]')
    expect(scripts.length).toBeGreaterThan(0)
  })

  it('should handle pathname changes correctly', () => {
    const { rerender } = render(<GoogleAnalytics />)
    
    // Múltiplas mudanças de pathname
    const paths = ['/glossario', '/metodo-46', '/base-de-conhecimento', '/']
    
    paths.forEach(path => {
      ;(usePathname as jest.Mock).mockReturnValue(path)
      rerender(<GoogleAnalytics />)
      expect(trackPageView).toHaveBeenCalledWith(path)
    })
  })

  it('should not track page view if pathname is null', () => {
    ;(usePathname as jest.Mock).mockReturnValue(null)
    
    render(<GoogleAnalytics />)
    
    // Deve ter sido chamado apenas na inicialização, não para pathname null
    expect(trackPageView).toHaveBeenCalledTimes(0)
  })

  it('should initialize only once even with multiple renders', () => {
    const { rerender } = render(<GoogleAnalytics />)
    
    rerender(<GoogleAnalytics />)
    rerender(<GoogleAnalytics />)
    
    // initGA e initializeConsentMode devem ser chamados apenas uma vez
    expect(initializeConsentMode).toHaveBeenCalledTimes(1)
    expect(initGA).toHaveBeenCalledTimes(1)
  })
})
