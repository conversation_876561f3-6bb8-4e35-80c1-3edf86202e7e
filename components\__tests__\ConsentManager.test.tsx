import { render, screen, fireEvent } from '@testing-library/react'
import { ConsentManager } from '../ConsentManager'

// Mock dos componentes filhos
jest.mock('../CookieBanner', () => ({
  CookieBanner: ({ onOpenSettings }: { onOpenSettings: () => void }) => (
    <div data-testid="cookie-banner">
      <button onClick={onOpenSettings} data-testid="open-settings">
        Configurações
      </button>
    </div>
  ),
}))

jest.mock('../CookieSettingsModal', () => ({
  CookieSettingsModal: ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => (
    isOpen ? (
      <div data-testid="cookie-settings-modal">
        <button onClick={onClose} data-testid="close-modal">
          Fechar
        </button>
      </div>
    ) : null
  ),
}))

describe('ConsentManager', () => {
  it('should render cookie banner', () => {
    render(<ConsentManager />)
    
    expect(screen.getByTestId('cookie-banner')).toBeInTheDocument()
  })

  it('should not show settings modal initially', () => {
    render(<ConsentManager />)
    
    expect(screen.queryByTestId('cookie-settings-modal')).not.toBeInTheDocument()
  })

  it('should open settings modal when requested', () => {
    render(<ConsentManager />)
    
    fireEvent.click(screen.getByTestId('open-settings'))
    
    expect(screen.getByTestId('cookie-settings-modal')).toBeInTheDocument()
  })

  it('should close settings modal when requested', () => {
    render(<ConsentManager />)
    
    // Abrir modal
    fireEvent.click(screen.getByTestId('open-settings'))
    expect(screen.getByTestId('cookie-settings-modal')).toBeInTheDocument()
    
    // Fechar modal
    fireEvent.click(screen.getByTestId('close-modal'))
    expect(screen.queryByTestId('cookie-settings-modal')).not.toBeInTheDocument()
  })

  it('should handle multiple open/close cycles', () => {
    render(<ConsentManager />)
    
    // Ciclo 1
    fireEvent.click(screen.getByTestId('open-settings'))
    expect(screen.getByTestId('cookie-settings-modal')).toBeInTheDocument()
    
    fireEvent.click(screen.getByTestId('close-modal'))
    expect(screen.queryByTestId('cookie-settings-modal')).not.toBeInTheDocument()
    
    // Ciclo 2
    fireEvent.click(screen.getByTestId('open-settings'))
    expect(screen.getByTestId('cookie-settings-modal')).toBeInTheDocument()
    
    fireEvent.click(screen.getByTestId('close-modal'))
    expect(screen.queryByTestId('cookie-settings-modal')).not.toBeInTheDocument()
  })
})
