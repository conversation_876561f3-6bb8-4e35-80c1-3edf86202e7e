import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import Home from '@/app/page'

// Mock das funções de analytics
jest.mock('@/lib/analytics', () => ({
  trackButtonClick: jest.fn(),
  trackRecipeGenerated: jest.fn(),
  trackRecipeCompleted: jest.fn(),
  trackRecipeShared: jest.fn(),
  trackRecipeTimer: jest.fn(),
}))

import {
  trackButtonClick,
  trackRecipeGenerated,
  trackRecipeTimer
} from '@/lib/analytics'

describe('Calculator Integration Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should complete full recipe generation flow', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    // 1. Verificar estado inicial
    expect(screen.getByDisplayValue('15')).toBeInTheDocument() // café
    expect(screen.getByDisplayValue('225')).toBeInTheDocument() // água
    
    // 2. Alterar quantidade de café
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    await user.clear(cafeInput)
    await user.type(cafeInput, '20')
    
    // 3. Verificar recálculo automático da água
    await waitFor(() => {
      const aguaInput = screen.getByLabelText(/quantidade de água/i)
      expect(aguaInput).toHaveValue('300') // 20 * 15 = 300
    })
    
    // 4. Selecionar perfil de sabor diferente
    const acidoButton = screen.getByText('Ácido')
    await user.click(acidoButton)
    expect(acidoButton).toHaveClass('bg-primary')
    
    // 5. Selecionar perfil de corpo diferente
    const encorpadoButton = screen.getByText('Encorpado')
    await user.click(encorpadoButton)
    expect(encorpadoButton).toHaveClass('bg-primary')
    
    // 6. Gerar receita
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    // 7. Verificar se receita foi gerada
    await waitFor(() => {
      expect(screen.getByText(/etapa 1/i)).toBeInTheDocument()
      expect(trackRecipeGenerated).toHaveBeenCalledWith({
        cafe: '20g',
        agua: '300ml',
        proporcao: '1:15',
        perfilSabor: 'Ácido',
        perfilCorpo: 'Encorpado'
      })
    })
    
    // 8. Verificar controles do timer
    expect(screen.getByText(/iniciar/i)).toBeInTheDocument()
    expect(screen.getByText(/reiniciar/i)).toBeInTheDocument()
    expect(screen.getByText(/compartilhar/i)).toBeInTheDocument()
  })

  it('should handle timer flow correctly', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    // Gerar receita primeiro
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText(/iniciar/i)).toBeInTheDocument()
    })
    
    // Iniciar timer
    const playButton = screen.getByText(/iniciar/i)
    await user.click(playButton)
    
    // Verificar mudança para pausar
    await waitFor(() => {
      expect(screen.getByText(/pausar/i)).toBeInTheDocument()
      expect(trackRecipeTimer).toHaveBeenCalledWith('start', 1)
    })
    
    // Pausar timer
    const pauseButton = screen.getByText(/pausar/i)
    await user.click(pauseButton)
    
    // Verificar mudança para retomar
    await waitFor(() => {
      expect(screen.getByText(/retomar/i)).toBeInTheDocument()
      expect(trackRecipeTimer).toHaveBeenCalledWith('pause', 1)
    })
    
    // Reiniciar receita
    const resetButton = screen.getByText(/reiniciar/i)
    await user.click(resetButton)
    
    await waitFor(() => {
      expect(screen.getByText(/gerar receita/i)).toBeInTheDocument()
      expect(trackButtonClick).toHaveBeenCalledWith('reiniciar_receita', 'calculadora')
    })
  })

  it('should handle proportion locking correctly', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    const aguaInput = screen.getByLabelText(/quantidade de água/i)
    const proporcaoInput = screen.getByLabelText(/proporção/i)
    
    // Verificar estado inicial (água e proporção bloqueadas)
    expect(aguaInput).toHaveClass('cursor-not-allowed')
    expect(proporcaoInput).toHaveClass('cursor-not-allowed')
    
    // Desbloquear proporção
    const unlockButtons = screen.getAllByRole('button').filter(btn => 
      btn.querySelector('svg') // botões com ícones de lock/unlock
    )
    
    // Encontrar e clicar no botão de unlock da proporção
    const proporcaoUnlockButton = unlockButtons[1] // segundo botão de unlock
    await user.click(proporcaoUnlockButton)
    
    expect(proporcaoInput).not.toHaveClass('cursor-not-allowed')
    
    // Alterar proporção
    await user.clear(proporcaoInput)
    await user.type(proporcaoInput, '12')
    
    // Alterar café e verificar recálculo com nova proporção
    await user.clear(cafeInput)
    await user.type(cafeInput, '25')
    
    await waitFor(() => {
      expect(aguaInput).toHaveValue('300') // 25 * 12 = 300
    })
  })

  it('should handle navigation menu correctly', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    // Abrir menu
    const menuButton = screen.getByLabelText(/abrir menu/i)
    await user.click(menuButton)
    
    // Verificar itens do menu
    await waitFor(() => {
      expect(screen.getByText(/glossário/i)).toBeInTheDocument()
      expect(screen.getByText(/método 4:6/i)).toBeInTheDocument()
      expect(screen.getByText(/base de conhecimento/i)).toBeInTheDocument()
    })
    
    // Verificar itens em construção
    const metodButton = screen.getByText(/método 4:6/i)
    expect(metodButton).toHaveClass('opacity-50')
    expect(metodButton).toHaveClass('cursor-not-allowed')
    
    // Fechar menu clicando fora
    const overlay = screen.getByTestId('menu-overlay') || document.body
    await user.click(overlay)
    
    await waitFor(() => {
      expect(screen.queryByText(/glossário/i)).not.toBeInTheDocument()
    })
  })

  it('should handle share functionality', async () => {
    const user = userEvent.setup()
    
    // Mock do clipboard
    const mockWriteText = jest.fn()
    Object.assign(navigator, {
      clipboard: {
        writeText: mockWriteText,
      },
    })
    
    render(<Home />)
    
    // Gerar receita
    const generateButton = screen.getByText(/gerar receita/i)
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText(/compartilhar/i)).toBeInTheDocument()
    })
    
    // Abrir modal de compartilhamento
    const shareButton = screen.getByText(/compartilhar/i)
    await user.click(shareButton)
    
    await waitFor(() => {
      expect(screen.getByText(/copiar receita/i)).toBeInTheDocument()
    })
    
    // Copiar receita
    const copyButton = screen.getByText(/copiar receita/i)
    await user.click(copyButton)
    
    expect(mockWriteText).toHaveBeenCalled()
  })

  it('should validate input constraints', async () => {
    const user = userEvent.setup()
    render(<Home />)
    
    const cafeInput = screen.getByLabelText(/quantidade de café/i)
    
    // Testar valores inválidos
    await user.clear(cafeInput)
    await user.type(cafeInput, '0')
    
    // Valor deve ser ajustado para mínimo válido
    await waitFor(() => {
      expect(cafeInput).not.toHaveValue('0')
    })
    
    // Testar valor muito alto
    await user.clear(cafeInput)
    await user.type(cafeInput, '999')
    
    // Valor deve ser limitado
    await waitFor(() => {
      const value = parseInt(cafeInput.getAttribute('value') || '0')
      expect(value).toBeLessThanOrEqual(100) // assumindo limite de 100g
    })
  })
})
