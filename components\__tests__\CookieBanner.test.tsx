import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { CookieBanner } from '../CookieBanner'

// Mock das funções de consent
jest.mock('@/lib/consent', () => ({
  hasUserMadeChoice: jest.fn(),
  acceptAllCookies: jest.fn(),
  rejectAllCookies: jest.fn(),
}))

import { hasUserMadeChoice, acceptAllCookies, rejectAllCookies } from '@/lib/consent'

describe('CookieBanner', () => {
  const mockOnOpenSettings = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should not show banner if user already made choice', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(true)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    // Avançar timer para simular o delay
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should show banner if user has not made choice', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    // Avançar timer para simular o delay
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/cookies/i)).toBeInTheDocument()
    })
  })

  it('should call acceptAllCookies when accept button is clicked', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/aceitar todos/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/aceitar todos/i))
    
    expect(acceptAllCookies).toHaveBeenCalled()
  })

  it('should call rejectAllCookies when reject button is clicked', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/rejeitar todos/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/rejeitar todos/i))
    
    expect(rejectAllCookies).toHaveBeenCalled()
  })

  it('should call onOpenSettings when customize button is clicked', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/personalizar/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/personalizar/i))
    
    expect(mockOnOpenSettings).toHaveBeenCalled()
  })

  it('should hide banner after accepting cookies', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/aceitar todos/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/aceitar todos/i))
    
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should hide banner after rejecting cookies', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/rejeitar todos/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/rejeitar todos/i))
    
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should hide banner after customizing settings', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      expect(screen.getByText(/personalizar/i)).toBeInTheDocument()
    })
    
    fireEvent.click(screen.getByText(/personalizar/i))
    
    await waitFor(() => {
      expect(screen.queryByText(/cookies/i)).not.toBeInTheDocument()
    })
  })

  it('should have proper accessibility attributes', async () => {
    ;(hasUserMadeChoice as jest.Mock).mockReturnValue(false)
    
    render(<CookieBanner onOpenSettings={mockOnOpenSettings} />)
    
    jest.advanceTimersByTime(200)
    
    await waitFor(() => {
      const banner = screen.getByRole('dialog')
      expect(banner).toHaveAttribute('aria-label')
      
      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        expect(button).toBeVisible()
      })
    })
  })
})
